package com.wexl.erp.reportcard;

import com.wexl.dps.learningmilestones.model.LmrCategoryGrade;
import com.wexl.dps.learningmilestones.model.LmrCategoryGradeAttribute;
import com.wexl.dps.learningmilestones.repository.CategoryGradeAttributeRepository;
import com.wexl.dps.learningmilestones.repository.LmrCategoryAttributeDefinitionRepository;
import com.wexl.dps.learningmilestones.repository.LmrCategoryGradeRepository;
import com.wexl.dps.reportcard.BaseReportCardDefinition;
import com.wexl.erp.reportcard.dto.ScholarsReportCardDto;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.wexl.retail.subjects.repository.SubjectsMetaDataRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class ScholarsReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final SubjectsMetaDataRepository subjectsMetaDataRepository;
  private final LmrCategoryGradeRepository categoryGradeRepository;
  private final CategoryGradeAttributeRepository lmrCategoryGradeAttributeRepository;
  private final LmrCategoryAttributeDefinitionRepository lmrCategoryAttributeDefinitionRepository;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var student = user.getStudentInfo();
    var header = buildScholarsHeader(student, org);
    var body = buildScholarsBody(student, org ,request);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("scholors-1st-2nd-report-card.xml");
  }

  private ScholarsReportCardDto.Header buildScholarsHeader(Student student, Organization org) {
    Optional<StudentAttributeValueModel> admissionNo =
        getStudentAttributeValue(student, "admission_no");

    return ScholarsReportCardDto.Header.builder()
        .schoolName(org.getName())
        .logo(org.getLogo())
        .build();
  }

  private ScholarsReportCardDto.Body buildScholarsBody(Student student, Organization org,ReportCardDto.Request request) {
    var subjectMetadata =
            subjectsMetaDataRepository.findById(request.offlineTestDefinitionId()).orElseThrow();
    List<LmrCategoryGrade> lmrCategoryGrades =
            categoryGradeRepository.getAllByGradeSlugAndSubjectMetadataIdAndTerm(
                    student.getSection().getGradeSlug(), subjectMetadata.getId(), request.termId());
    List<Long> categoryGradeIds = lmrCategoryGrades.stream().map(LmrCategoryGrade::getLmrCategoryId).toList();
     List<LmrCategoryGradeAttribute> lmrCategoryGradeAttributes = lmrCategoryGradeAttributeRepository.findByLmrCategoryGradeIdIn(categoryGradeIds);


     var lmrCategoryAttributeDefinitionMap = l
    Optional<StudentAttributeValueModel> dateOfBirth =
        getStudentAttributeValue(student, "date_of_birth");
    Optional<StudentAttributeValueModel> bloodGroup =
        getStudentAttributeValue(student, "blood_group");
    Optional<StudentAttributeValueModel> mobileNumber =
        getStudentAttributeValue(student, "mobile_number");
    Optional<StudentAttributeValueModel> address = getStudentAttributeValue(student, "address");
    Optional<StudentAttributeValueModel> fatherName =
        getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> motherName =
        getStudentAttributeValue(student, "mother_name");

    return ScholarsReportCardDto.Body.builder()
        .name(student.getUserInfo().getFirstName() + " " + student.getUserInfo().getLastName())
        .sectionName(student.getSection() != null ? student.getSection().getName() : "")
        .gradeName(student.getSection().getGradeName())
        .admissionNumber(student.getRollNumber() != null ? student.getRollNumber() : "")
        .fatherName(fatherName.map(StudentAttributeValueModel::getValue).orElse(""))
        .dateOfBirth(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(""))
        .rollNo(student.getClassRollNumber() != null ? student.getClassRollNumber() : "")
        .motherName(motherName.map(StudentAttributeValueModel::getValue).orElse(""))
        .bloodGroup(bloodGroup.map(StudentAttributeValueModel::getValue).orElse(""))
        .mobileNumber(mobileNumber.map(StudentAttributeValueModel::getValue).orElse(""))
        .address(address.map(StudentAttributeValueModel::getValue).orElse(""))
        .subjects(buildSubjects())
        .build();
  }

  private ScholarsReportCardDto.Body buildSubjects(Student student, Organization org) {}
}
