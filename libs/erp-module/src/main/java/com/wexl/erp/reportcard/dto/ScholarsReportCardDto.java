package com.wexl.erp.reportcard.dto;

import java.util.List;
import lombok.Builder;

public record ScholarsReportCardDto() {

  @Builder
  public record Response(Header header, Body body) {}

  @Builder
  public record Header(String logo, String schoolName) {}

  @Builder
  public record Body(
      String name,
      String sectionName,
      String gradeName,
      String admissionNumber,
      String fatherName,
      String dateOfBirth,
      String rollNo,
      String motherName,
      String bloodGroup,
      String mobileNumber,
      String address,
      List<Subject> subjects) {}

  @Builder
  public record Subject(String name, String term, List<SkillGroup> skillGroups) {}

  @Builder
  public record SkillGroup(String groupName, List<Skill> skills) {}

  @Builder
  public record Skill(String name, String grade) {}
}
